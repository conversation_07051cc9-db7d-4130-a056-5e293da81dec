#!/usr/bin/env python3
"""
ConvNeXt特征贡献度分析脚本
对比融合ConvNeXt前后的特征值，计算ConvNeXt的贡献百分比
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os
from typing import Dict, Tuple, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.FFM_change import FeatureFusionModule

class ConvNeXtContributionAnalyzer:
    """ConvNeXt特征贡献度分析器"""
    
    def __init__(self, device='cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
    
    def calculate_contribution_metrics(self, features_without: torch.Tensor, 
                                     features_with: torch.Tensor) -> Dict[str, float]:
        """
        计算ConvNeXt特征的贡献度指标
        
        Args:
            features_without: 不使用ConvNeXt的特征 (B, C, H, W)
            features_with: 使用ConvNeXt的特征 (B, C, H, W)
            
        Returns:
            包含各种贡献度指标的字典
        """
        # 计算绝对差异
        abs_diff = torch.abs(features_with - features_without)
        
        # 计算相对差异
        rel_diff = abs_diff / (torch.abs(features_without) + 1e-8)
        
        # 计算各种统计指标
        metrics = {
            # 绝对差异指标
            'abs_diff_mean': abs_diff.mean().item(),
            'abs_diff_std': abs_diff.std().item(),
            'abs_diff_max': abs_diff.max().item(),
            'abs_diff_min': abs_diff.min().item(),
            
            # 相对差异指标
            'rel_diff_mean': rel_diff.mean().item(),
            'rel_diff_std': rel_diff.std().item(),
            'rel_diff_max': rel_diff.max().item(),
            'rel_diff_median': rel_diff.median().item(),
            
            # 贡献百分比（基于L2范数）
            'l2_contribution_percent': (torch.norm(abs_diff) / torch.norm(features_without) * 100).item(),
            
            # 贡献百分比（基于均值）
            'mean_contribution_percent': (abs_diff.mean() / torch.abs(features_without).mean() * 100).item(),
            
            # 余弦相似度
            'cosine_similarity': nn.functional.cosine_similarity(
                features_without.flatten(1), features_with.flatten(1), dim=1
            ).mean().item(),
            
            # 皮尔逊相关系数
            'pearson_correlation': self._calculate_pearson_correlation(
                features_without.flatten(), features_with.flatten()
            ),
            
            # 信噪比 (SNR)
            'snr_db': self._calculate_snr(features_without, abs_diff),
            
            # 影响的像素百分比（变化超过阈值的像素比例）
            'affected_pixels_percent_1': (abs_diff > 0.01).float().mean().item() * 100,
            'affected_pixels_percent_5': (abs_diff > 0.05).float().mean().item() * 100,
            'affected_pixels_percent_10': (abs_diff > 0.1).float().mean().item() * 100,
        }
        
        return metrics
    
    def _calculate_pearson_correlation(self, x: torch.Tensor, y: torch.Tensor) -> float:
        """计算皮尔逊相关系数"""
        x_mean = x.mean()
        y_mean = y.mean()
        
        numerator = ((x - x_mean) * (y - y_mean)).sum()
        denominator = torch.sqrt(((x - x_mean) ** 2).sum() * ((y - y_mean) ** 2).sum())
        
        return (numerator / (denominator + 1e-8)).item()
    
    def _calculate_snr(self, signal: torch.Tensor, noise: torch.Tensor) -> float:
        """计算信噪比 (dB)"""
        signal_power = torch.mean(signal ** 2)
        noise_power = torch.mean(noise ** 2)
        snr = 10 * torch.log10(signal_power / (noise_power + 1e-8))
        return snr.item()
    
    def analyze_channel_wise_contribution(self, features_without: torch.Tensor, 
                                        features_with: torch.Tensor) -> Dict[str, np.ndarray]:
        """
        分析每个通道的ConvNeXt贡献度
        
        Args:
            features_without: 不使用ConvNeXt的特征 (B, C, H, W)
            features_with: 使用ConvNeXt的特征 (B, C, H, W)
            
        Returns:
            每个通道的贡献度统计
        """
        B, C, H, W = features_without.shape
        
        # 计算每个通道的差异
        abs_diff = torch.abs(features_with - features_without)  # (B, C, H, W)
        
        # 按通道计算统计指标
        channel_metrics = {
            'channel_abs_diff_mean': abs_diff.mean(dim=(0, 2, 3)).cpu().numpy(),  # (C,)
            'channel_abs_diff_std': abs_diff.std(dim=(0, 2, 3)).cpu().numpy(),   # (C,)
            'channel_contribution_percent': (
                abs_diff.mean(dim=(0, 2, 3)) / 
                torch.abs(features_without).mean(dim=(0, 2, 3)) * 100
            ).cpu().numpy(),  # (C,)
        }
        
        return channel_metrics
    
    def analyze_spatial_contribution(self, features_without: torch.Tensor, 
                                   features_with: torch.Tensor) -> Dict[str, np.ndarray]:
        """
        分析空间位置的ConvNeXt贡献度
        
        Args:
            features_without: 不使用ConvNeXt的特征 (B, C, H, W)
            features_with: 使用ConvNeXt的特征 (B, C, H, W)
            
        Returns:
            空间位置的贡献度热图
        """
        B, C, H, W = features_without.shape
        
        # 计算空间位置的平均差异
        abs_diff = torch.abs(features_with - features_without)  # (B, C, H, W)
        
        # 按空间位置计算平均差异
        spatial_metrics = {
            'spatial_abs_diff_mean': abs_diff.mean(dim=(0, 1)).cpu().numpy(),  # (H, W)
            'spatial_contribution_percent': (
                abs_diff.mean(dim=(0, 1)) / 
                torch.abs(features_without).mean(dim=(0, 1)) * 100
            ).cpu().numpy(),  # (H, W)
        }
        
        return spatial_metrics
    
    def test_different_convnext_strengths(self, dim: int = 96, batch_size: int = 4, 
                                        H: int = 32, W: int = 32) -> Dict[str, List[float]]:
        """
        测试不同ConvNeXt特征强度的贡献度
        
        Args:
            dim: 特征维度
            batch_size: 批次大小
            H, W: 特征图尺寸
            
        Returns:
            不同强度下的贡献度指标
        """
        print(f"\n=== 测试不同ConvNeXt特征强度的贡献度 ===")
        print(f"特征维度: {dim}, 批次大小: {batch_size}, 特征图尺寸: {H}x{W}")
        
        # 创建测试数据
        rgb_features = torch.randn(batch_size, dim, H, W).to(self.device)
        depth_features = torch.randn(batch_size, dim, H, W).to(self.device)
        
        # 创建FFM模块
        ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(self.device)
        
        # 测试不同强度的ConvNeXt特征
        strengths = [0.1, 0.5, 1.0, 1.5, 2.0, 3.0]
        strength_results = {
            'strengths': strengths,
            'l2_contribution_percent': [],
            'mean_contribution_percent': [],
            'cosine_similarity': [],
            'affected_pixels_percent_5': [],
        }
        
        with torch.no_grad():
            # 基准：不使用ConvNeXt
            features_baseline = ffm(rgb_features, depth_features, None)
            
            for strength in strengths:
                # 创建不同强度的ConvNeXt特征
                convnext_features = torch.randn(batch_size, dim, H, W).to(self.device) * strength
                
                # 使用ConvNeXt特征
                features_with_convnext = ffm(rgb_features, depth_features, convnext_features)
                
                # 计算贡献度指标
                metrics = self.calculate_contribution_metrics(features_baseline, features_with_convnext)
                
                # 记录关键指标
                strength_results['l2_contribution_percent'].append(metrics['l2_contribution_percent'])
                strength_results['mean_contribution_percent'].append(metrics['mean_contribution_percent'])
                strength_results['cosine_similarity'].append(metrics['cosine_similarity'])
                strength_results['affected_pixels_percent_5'].append(metrics['affected_pixels_percent_5'])
                
                print(f"强度 {strength:.1f}: L2贡献={metrics['l2_contribution_percent']:.2f}%, "
                      f"均值贡献={metrics['mean_contribution_percent']:.2f}%, "
                      f"余弦相似度={metrics['cosine_similarity']:.4f}")
        
        return strength_results
    
    def comprehensive_analysis(self, dim: int = 96, batch_size: int = 4, 
                             H: int = 32, W: int = 32) -> Dict:
        """
        综合分析ConvNeXt特征贡献度
        
        Args:
            dim: 特征维度
            batch_size: 批次大小
            H, W: 特征图尺寸
            
        Returns:
            完整的分析结果
        """
        print(f"\n=== ConvNeXt特征贡献度综合分析 ===")
        print(f"特征维度: {dim}, 批次大小: {batch_size}, 特征图尺寸: {H}x{W}")
        
        # 创建测试数据
        rgb_features = torch.randn(batch_size, dim, H, W).to(self.device)
        depth_features = torch.randn(batch_size, dim, H, W).to(self.device)
        convnext_features = torch.randn(batch_size, dim, H, W).to(self.device)
        
        print(f"RGB特征统计: 均值={rgb_features.mean():.6f}, 标准差={rgb_features.std():.6f}")
        print(f"Depth特征统计: 均值={depth_features.mean():.6f}, 标准差={depth_features.std():.6f}")
        print(f"ConvNeXt特征统计: 均值={convnext_features.mean():.6f}, 标准差={convnext_features.std():.6f}")
        
        # 创建FFM模块
        ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(self.device)
        
        with torch.no_grad():
            # 不使用ConvNeXt特征
            features_without = ffm(rgb_features, depth_features, None)
            
            # 使用ConvNeXt特征
            features_with = ffm(rgb_features, depth_features, convnext_features)
            
            print(f"\n特征输出统计:")
            print(f"不使用ConvNeXt: 均值={features_without.mean():.6f}, 标准差={features_without.std():.6f}")
            print(f"使用ConvNeXt: 均值={features_with.mean():.6f}, 标准差={features_with.std():.6f}")
            
            # 1. 整体贡献度分析
            overall_metrics = self.calculate_contribution_metrics(features_without, features_with)
            
            # 2. 通道级贡献度分析
            channel_metrics = self.analyze_channel_wise_contribution(features_without, features_with)
            
            # 3. 空间级贡献度分析
            spatial_metrics = self.analyze_spatial_contribution(features_without, features_with)
            
            # 4. 不同强度测试
            strength_results = self.test_different_convnext_strengths(dim, batch_size, H, W)
            
            return {
                'overall_metrics': overall_metrics,
                'channel_metrics': channel_metrics,
                'spatial_metrics': spatial_metrics,
                'strength_results': strength_results,
                'features_without': features_without.cpu(),
                'features_with': features_with.cpu(),
            }
    
    def print_detailed_report(self, results: Dict):
        """打印详细的分析报告"""
        print(f"\n" + "="*60)
        print(f"ConvNeXt特征贡献度详细分析报告")
        print(f"="*60)
        
        overall = results['overall_metrics']
        
        print(f"\n📊 整体贡献度指标:")
        print(f"  L2范数贡献百分比: {overall['l2_contribution_percent']:.2f}%")
        print(f"  均值贡献百分比: {overall['mean_contribution_percent']:.2f}%")
        print(f"  余弦相似度: {overall['cosine_similarity']:.6f}")
        print(f"  皮尔逊相关系数: {overall['pearson_correlation']:.6f}")
        print(f"  信噪比: {overall['snr_db']:.2f} dB")
        
        print(f"\n📈 差异统计:")
        print(f"  平均绝对差异: {overall['abs_diff_mean']:.6f}")
        print(f"  最大绝对差异: {overall['abs_diff_max']:.6f}")
        print(f"  平均相对差异: {overall['rel_diff_mean']:.6f}")
        print(f"  中位相对差异: {overall['rel_diff_median']:.6f}")
        
        print(f"\n🎯 影响范围:")
        print(f"  变化>1%的像素比例: {overall['affected_pixels_percent_1']:.2f}%")
        print(f"  变化>5%的像素比例: {overall['affected_pixels_percent_5']:.2f}%")
        print(f"  变化>10%的像素比例: {overall['affected_pixels_percent_10']:.2f}%")
        
        # 通道级分析
        channel = results['channel_metrics']
        print(f"\n🔍 通道级分析:")
        print(f"  最高贡献通道: {np.argmax(channel['channel_contribution_percent'])}, "
              f"贡献度: {np.max(channel['channel_contribution_percent']):.2f}%")
        print(f"  最低贡献通道: {np.argmin(channel['channel_contribution_percent'])}, "
              f"贡献度: {np.min(channel['channel_contribution_percent']):.2f}%")
        print(f"  通道贡献度标准差: {np.std(channel['channel_contribution_percent']):.2f}%")
        
        # 空间级分析
        spatial = results['spatial_metrics']
        print(f"\n🗺️ 空间级分析:")
        print(f"  最高贡献位置: {np.unravel_index(np.argmax(spatial['spatial_contribution_percent']), spatial['spatial_contribution_percent'].shape)}")
        print(f"  最高贡献度: {np.max(spatial['spatial_contribution_percent']):.2f}%")
        print(f"  空间贡献度标准差: {np.std(spatial['spatial_contribution_percent']):.2f}%")
        
        # 评估结论
        print(f"\n🎯 评估结论:")
        l2_contrib = overall['l2_contribution_percent']
        if l2_contrib > 20:
            print(f"  ✅ ConvNeXt特征有显著影响 (L2贡献 {l2_contrib:.1f}% > 20%)")
        elif l2_contrib > 10:
            print(f"  ⚠️ ConvNeXt特征有中等影响 (L2贡献 {l2_contrib:.1f}% > 10%)")
        elif l2_contrib > 5:
            print(f"  ⚠️ ConvNeXt特征有轻微影响 (L2贡献 {l2_contrib:.1f}% > 5%)")
        else:
            print(f"  ❌ ConvNeXt特征影响很小 (L2贡献 {l2_contrib:.1f}% < 5%)")
        
        cosine_sim = overall['cosine_similarity']
        if cosine_sim > 0.95:
            print(f"  ✅ 特征保持高度一致性 (余弦相似度 {cosine_sim:.4f} > 0.95)")
        elif cosine_sim > 0.90:
            print(f"  ⚠️ 特征保持良好一致性 (余弦相似度 {cosine_sim:.4f} > 0.90)")
        else:
            print(f"  ❌ 特征一致性较低 (余弦相似度 {cosine_sim:.4f} < 0.90)")

def main():
    """主函数"""
    print("ConvNeXt特征贡献度分析")
    print("="*50)
    
    # 创建分析器
    analyzer = ConvNeXtContributionAnalyzer()
    
    # 执行综合分析
    results = analyzer.comprehensive_analysis(dim=96, batch_size=4, H=32, W=32)
    
    # 打印详细报告
    analyzer.print_detailed_report(results)
    
    print(f"\n" + "="*60)
    print(f"🎉 分析完成！ConvNeXt特征贡献度分析报告已生成。")
    print(f"="*60)

if __name__ == "__main__":
    main()
