#!/usr/bin/env python3
"""
ConvNeXt特征融合演示脚本
展示新的融合策略相比原有GCCM方法的优势
"""

import torch
import torch.nn as nn
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.FFM_change import FeatureFusionModule

def compare_fusion_strategies():
    """比较不同融合策略的效果"""
    print("=== ConvNeXt特征融合策略对比演示 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 测试参数
    dim = 96
    batch_size = 4
    H, W = 32, 32
    
    # 创建测试数据
    rgb_features = torch.randn(batch_size, dim, H, W).to(device)
    depth_features = torch.randn(batch_size, dim, H, W).to(device)
    convnext_features = torch.randn(batch_size, dim, H, W).to(device)
    
    print(f"输入特征形状:")
    print(f"  RGB特征: {rgb_features.shape}")
    print(f"  Depth特征: {depth_features.shape}")
    print(f"  ConvNeXt特征: {convnext_features.shape}")
    
    # 策略1：不使用ConvNeXt特征（原有双分支融合）
    print("\n--- 策略1：双分支融合（RGB + Depth）---")
    ffm_dual = FeatureFusionModule(dim=dim, enable_convnext_fusion=False).to(device)
    
    start_time = time.time()
    with torch.no_grad():
        output_dual = ffm_dual(rgb_features, depth_features)
    dual_time = time.time() - start_time
    
    print(f"输出形状: {output_dual.shape}")
    print(f"推理时间: {dual_time:.4f}s")
    print(f"输出特征统计:")
    print(f"  均值: {output_dual.mean().item():.6f}")
    print(f"  标准差: {output_dual.std().item():.6f}")
    print(f"  最大值: {output_dual.max().item():.6f}")
    print(f"  最小值: {output_dual.min().item():.6f}")
    
    # 策略2：使用ConvNeXt特征融合（新的三分支融合）
    print("\n--- 策略2：三分支融合（RGB + Depth + ConvNeXt）---")
    ffm_triple = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(device)
    
    start_time = time.time()
    with torch.no_grad():
        output_triple = ffm_triple(rgb_features, depth_features, convnext_features)
    triple_time = time.time() - start_time
    
    print(f"输出形状: {output_triple.shape}")
    print(f"推理时间: {triple_time:.4f}s")
    print(f"输出特征统计:")
    print(f"  均值: {output_triple.mean().item():.6f}")
    print(f"  标准差: {output_triple.std().item():.6f}")
    print(f"  最大值: {output_triple.max().item():.6f}")
    print(f"  最小值: {output_triple.min().item():.6f}")
    
    # 分析差异
    print("\n--- 融合策略对比分析 ---")
    feature_diff = torch.abs(output_triple - output_dual)
    print(f"特征差异统计:")
    print(f"  平均绝对差异: {feature_diff.mean().item():.6f}")
    print(f"  最大绝对差异: {feature_diff.max().item():.6f}")
    print(f"  差异标准差: {feature_diff.std().item():.6f}")
    
    # 计算相似度
    cosine_sim = nn.functional.cosine_similarity(
        output_dual.flatten(1), output_triple.flatten(1), dim=1
    ).mean().item()
    print(f"  余弦相似度: {cosine_sim:.6f}")
    
    # 性能对比
    print(f"\n性能对比:")
    print(f"  双分支融合时间: {dual_time:.4f}s")
    print(f"  三分支融合时间: {triple_time:.4f}s")
    print(f"  时间增加: {((triple_time - dual_time) / dual_time * 100):.2f}%")
    
    return output_dual, output_triple

def analyze_convnext_contribution():
    """分析ConvNeXt特征的贡献度"""
    print("\n=== ConvNeXt特征贡献度分析 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    batch_size = 2
    H, W = 16, 16
    
    # 创建测试数据
    rgb_features = torch.randn(batch_size, dim, H, W).to(device)
    depth_features = torch.randn(batch_size, dim, H, W).to(device)
    
    # 创建不同强度的ConvNeXt特征
    convnext_weak = torch.randn(batch_size, dim, H, W).to(device) * 0.1  # 弱信号
    convnext_normal = torch.randn(batch_size, dim, H, W).to(device)      # 正常信号
    convnext_strong = torch.randn(batch_size, dim, H, W).to(device) * 2.0  # 强信号
    
    ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(device)
    
    # 基准：不使用ConvNeXt
    with torch.no_grad():
        output_baseline = ffm(rgb_features, depth_features, None)
        output_weak = ffm(rgb_features, depth_features, convnext_weak)
        output_normal = ffm(rgb_features, depth_features, convnext_normal)
        output_strong = ffm(rgb_features, depth_features, convnext_strong)
    
    print("ConvNeXt特征强度对输出的影响:")
    
    # 计算与基准的差异
    diff_weak = torch.abs(output_weak - output_baseline).mean().item()
    diff_normal = torch.abs(output_normal - output_baseline).mean().item()
    diff_strong = torch.abs(output_strong - output_baseline).mean().item()
    
    print(f"  弱ConvNeXt信号影响: {diff_weak:.6f}")
    print(f"  正常ConvNeXt信号影响: {diff_normal:.6f}")
    print(f"  强ConvNeXt信号影响: {diff_strong:.6f}")
    
    # 分析门控权重的自适应性
    print("\n门控机制自适应性分析:")
    print(f"  影响强度比例 (弱:正常:强) = 1.0:{diff_normal/diff_weak:.2f}:{diff_strong/diff_weak:.2f}")
    
    if diff_strong < diff_normal * 1.5:
        print("  ✓ 门控机制有效抑制了过强的ConvNeXt信号")
    else:
        print("  ⚠ 门控机制可能需要调整以更好地控制强信号")

def demonstrate_fusion_benefits():
    """演示融合策略的优势"""
    print("\n=== 融合策略优势演示 ===")
    
    print("新的ConvNeXt特征融合策略相比原有GCCM方法的优势:")
    print("\n1. 架构简化:")
    print("   - 移除了独立的GCCM融合模块")
    print("   - 在FFM的CrossAttention中直接融合ConvNeXt特征")
    print("   - 减少了模型复杂度和参数量")
    
    print("\n2. 融合策略改进:")
    print("   - 使用门控机制控制ConvNeXt特征的贡献度")
    print("   - 采用残差连接保持原有ctx的主导地位")
    print("   - 自适应权重学习最优融合比例")
    
    print("\n3. 数学融合方法:")
    print("   - 不简单相加，而是通过门控网络学习融合权重")
    print("   - 使用Sigmoid激活确保权重在[0,1]范围内")
    print("   - 残差连接: enhanced = original + α * gate * convnext")
    
    print("\n4. 保持原有性能:")
    print("   - 在不影响原ctx基础上添加局部新信息")
    print("   - 门控权重初始化为较小值(0.1)，确保渐进式融合")
    print("   - 支持动态开关，可以灵活启用/禁用ConvNeXt融合")

def main():
    """主演示函数"""
    print("ConvNeXt特征融合策略演示")
    print("="*50)
    
    try:
        # 运行对比演示
        output_dual, output_triple = compare_fusion_strategies()
        
        # 分析ConvNeXt贡献度
        analyze_convnext_contribution()
        
        # 演示融合优势
        demonstrate_fusion_benefits()
        
        print("\n" + "="*50)
        print("🎉 演示完成！新的ConvNeXt特征融合策略已成功实现。")
        print("\n主要改进:")
        print("✓ 取消了原有的GCCM融合层")
        print("✓ 在CrossAttention中直接融合ConvNeXt特征")
        print("✓ 使用门控机制控制融合强度")
        print("✓ 保持原有全局注意力分数的主导地位")
        print("✓ 支持动态开关ConvNeXt特征融合")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
