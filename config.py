import os.path as osp
import os

if os.name == 'nt':  # Windows系统
    remoteip = os.popen('cd').read()
else:
    remoteip = os.popen('pwd').read()

class config:
    # 基础配置
    root_dir = os.path.abspath(os.path.join(os.getcwd(), './'))
    dataset_name = 'LEVIR_CD'
    num_classes = 6
    class_names = ['unchanged', 'water', 'ground', 'low_vegetation', 'tree', 'building']

    # 随机种子配置
    seed = 12345

    # 数据集路径配置
    dataset_path = osp.join(root_dir, 'datasets', dataset_name)
    rgb_root_folder = osp.join(dataset_path, 'RGB')
    rgb_format = '.jpg'
    gt_root_folder = osp.join(dataset_path, 'Label')
    gt_format = '.png'
    gt_transform = True  # True when label 0 is invalid
    x_root_folder = osp.join(dataset_path, 'Depth')  # 或者其他模态数据
    x_format = '.png'
    x_is_single_channel = True  # True for depth, thermal, SAR等单通道数据
    train_source = osp.join(dataset_path, "train.txt")
    eval_source = osp.join(dataset_path, "val.txt")
    is_test = False

    # 图像配置
    image_height = 256
    image_width = 256
    norm_mean = [0.485, 0.456, 0.406]  # ImageNet标准化参数
    norm_std = [0.229, 0.224, 0.225]   # ImageNet标准化参数

    # 模型配置
    backbone = 'swin_s'
    decoder = 'UPerHead'  # 解码器类型
    pretrained_model = osp.join(root_dir, 'pretrained', 'swin_small_patch4_window7_224.pth')
    
    # Triple Backbone Configuration - ConvNeXt-V2
    use_triple_backbone = True  # 启用三主干架构
    convnextv2_backbone_config = {
        'in_chans': 3,
        'num_classes': 0,  # 设置为0，因为我们只需要特征提取
        'depths': [3, 3, 27, 3],  # ConvNeXt-V2 Base配置
        'dims': [128, 256, 512, 1024],  # ConvNeXt-V2 Base维度
        'drop_path_rate': 0.2,
        'head_init_scale': 1.0,
        'out_indices': (0, 1, 2, 3),  # 输出四个阶段的特征
        'pretrained_path': osp.join(root_dir, 'convnextv2', 'convnextv2_base_22k_384_ema.pt')
    }
    
    # 训练配置 - 针对ConvNeXt-V2优化
    lr = 1e-4  # 适中的学习率
    lr_power = 0.9
    momentum = 0.9
    weight_decay = 0.01
    batch_size = 8  # ConvNeXt-V2可以支持更大的batch size
    nepochs = 200  # ConvNeXt-V2收敛更快
    num_train_imgs = 7120
    num_eval_imgs = 1024
    niters_per_epoch = num_train_imgs // batch_size + 1
    num_workers = 4
    train_scale_array = [0.5, 0.75, 1, 1.25, 1.5, 1.75]
    warm_up_epoch = 10  # 减少预热轮数

    # 优化器配置
    optimizer = 'AdamW'  # 优化器类型：'AdamW' 或 'SGDM'
    grad_clip = 1.0  # 梯度裁剪阈值

    # 数据集配置
    background = 255  # 背景类别标签（忽略索引）
    
    # 损失函数配置
    loss_type = 'simplified_combined'  # 损失函数类型：'ce', 'combined', 'adaptive', 'simplified_combined', 'multimodal_combined'

    # 损失函数权重配置
    loss_weights = {
        'ce_weight': 1.0,
        'focal_weight': 0.5,
        'dice_weight': 0.0,
        'lovasz_weight': 0.0,
        'boundary_weight': 0.0,
        'context_weight': 0.0
    }

    # Focal Loss参数
    focal_gamma = 2.0
    focal_alpha = None  # 可以设置为类别权重列表或None

    # 标签平滑
    label_smoothing = 0.0

    # 类别权重（可选）
    class_weights = None  # 例如：[1.0, 2.0, 1.5, 1.0, 1.0, 1.0] 对应6个类别

    # 简化组合损失函数的上下文参数
    context_kernel_size = 3
    context_dilation = 1

    # 自适应损失函数参数
    adaptive_initial_weights = {'ce_weight': 1.0, 'focal_weight': 0.5, 'dice_weight': 0.3, 'lovasz_weight': 0.2}
    adaptive_min_weight = 0.1
    adaptive_max_weight = 2.0
    adaptive_weight_decay = 0.01
    adaptive_temperature = 1.0
    adaptive_weight_lr_ratio = 0.1
    adaptive_freeze_after_epochs = None

    # 日志配置
    log_loss_components = True  # 是否记录损失组件详细信息
    
    # 评估配置
    eval_iter = 20
    eval_stride_rate = 2 / 3
    eval_scale_array = [1]
    eval_flip = False
    eval_crop_size = [256, 256]
    eval_batch_size = 8
    eval_frequency = 5  # 每5轮进行一次验证
    
    # 日志配置
    log_dir = osp.abspath('log_' + dataset_name + '_' + backbone + '_convnextv2')
    tb_dir = osp.abspath(osp.join(log_dir, "tb"))
    checkpoint_dir = osp.abspath(osp.join(log_dir, "checkpoint"))
    
    import time
    exp_time = time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime())
    log_file = log_dir + '/log_' + exp_time + '.log'
    val_log_file = log_dir + '/val_' + exp_time + '.log'

# 创建全局配置实例
config = config()
