#!/usr/bin/env python3
"""
调试ConvNeXt特征影响度的脚本
分析为什么ConvNeXt特征对原特征的影响很小
"""

import torch
import torch.nn as nn
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.FFM_change import ConvNeXtFusionGate, CrossAttention, FeatureFusionModule

def debug_fusion_gate():
    """调试ConvNeXt融合门控模块的内部参数"""
    print("=== 调试ConvNeXt融合门控模块 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    num_heads = 8
    batch_size = 2
    H, W = 16, 16
    N = H * W
    
    # 创建测试数据
    original_feature = torch.randn(batch_size, N, dim).to(device)
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
    
    print(f"原始特征统计: 均值={original_feature.mean().item():.6f}, 标准差={original_feature.std().item():.6f}")
    print(f"ConvNeXt特征统计: 均值={convnext_feature.mean().item():.6f}, 标准差={convnext_feature.std().item():.6f}")
    
    # 创建门控模块
    gate = ConvNeXtFusionGate(dim, num_heads).to(device)
    
    # 分析初始参数
    print(f"\n初始fusion_weight: {gate.fusion_weight.item():.6f}")
    
    with torch.no_grad():
        # 转换ConvNeXt特征
        convnext_seq = convnext_feature.flatten(2).transpose(1, 2)  # (B, H*W, C)
        convnext_proj = gate.convnext_proj(convnext_seq)
        
        print(f"ConvNeXt投影后统计: 均值={convnext_proj.mean().item():.6f}, 标准差={convnext_proj.std().item():.6f}")
        
        # 计算门控权重
        combined_features = torch.cat([original_feature, convnext_proj], dim=-1)
        gate_weights = gate.gate_net(combined_features)
        
        print(f"门控权重统计: 均值={gate_weights.mean().item():.6f}, 标准差={gate_weights.std().item():.6f}")
        print(f"门控权重范围: [{gate_weights.min().item():.6f}, {gate_weights.max().item():.6f}]")
        
        # 计算实际的融合贡献
        fusion_contribution = gate.fusion_weight * gate_weights * convnext_proj
        print(f"融合贡献统计: 均值={fusion_contribution.mean().item():.6f}, 标准差={fusion_contribution.std().item():.6f}")
        
        # 计算融合后的特征
        enhanced_feature = original_feature + fusion_contribution
        
        # 分析影响
        absolute_change = torch.abs(enhanced_feature - original_feature)
        relative_change = absolute_change / (torch.abs(original_feature) + 1e-8)
        
        print(f"\n影响分析:")
        print(f"绝对变化: 均值={absolute_change.mean().item():.6f}, 最大={absolute_change.max().item():.6f}")
        print(f"相对变化: 均值={relative_change.mean().item():.6f}, 最大={relative_change.max().item():.6f}")
        
        # 分析各个组件的贡献
        fusion_weight_contrib = gate.fusion_weight.item()
        gate_weight_contrib = gate_weights.mean().item()
        convnext_contrib = convnext_proj.std().item()
        
        print(f"\n各组件贡献:")
        print(f"fusion_weight: {fusion_weight_contrib:.6f}")
        print(f"gate_weights平均: {gate_weight_contrib:.6f}")
        print(f"convnext_proj标准差: {convnext_contrib:.6f}")
        print(f"总体贡献估计: {fusion_weight_contrib * gate_weight_contrib * convnext_contrib:.6f}")

def test_different_fusion_weights():
    """测试不同fusion_weight值的影响"""
    print("\n=== 测试不同fusion_weight值的影响 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    num_heads = 8
    batch_size = 2
    H, W = 16, 16
    N = H * W
    
    # 创建测试数据
    original_feature = torch.randn(batch_size, N, dim).to(device)
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
    
    # 测试不同的fusion_weight值
    fusion_weights = [0.01, 0.1, 0.5, 1.0, 2.0]
    
    for fw in fusion_weights:
        gate = ConvNeXtFusionGate(dim, num_heads).to(device)
        # 手动设置fusion_weight
        gate.fusion_weight.data = torch.tensor(fw)
        
        with torch.no_grad():
            enhanced_feature = gate(original_feature, convnext_feature)
            
            # 计算影响
            absolute_change = torch.abs(enhanced_feature - original_feature).mean().item()
            
            print(f"fusion_weight={fw:.2f}: 平均绝对变化={absolute_change:.6f}")

def analyze_cross_attention_impact():
    """分析CrossAttention中ConvNeXt特征的影响"""
    print("\n=== 分析CrossAttention中ConvNeXt特征的影响 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    num_heads = 8
    batch_size = 2
    H, W = 16, 16
    N = H * W
    
    # 创建测试数据
    x1 = torch.randn(batch_size, N, dim).to(device)
    x2 = torch.randn(batch_size, N, dim).to(device)
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
    
    # 创建CrossAttention
    cross_attn = CrossAttention(dim, num_heads, enable_convnext_fusion=True).to(device)
    
    with torch.no_grad():
        # 不使用ConvNeXt特征
        out1_orig, out2_orig = cross_attn(x1, x2, None)
        
        # 使用ConvNeXt特征
        out1_enhanced, out2_enhanced = cross_attn(x1, x2, convnext_feature)
        
        # 分析差异
        diff1 = torch.abs(out1_enhanced - out1_orig).mean().item()
        diff2 = torch.abs(out2_enhanced - out2_orig).mean().item()
        
        print(f"CrossAttention输出差异:")
        print(f"输出1平均绝对差异: {diff1:.6f}")
        print(f"输出2平均绝对差异: {diff2:.6f}")
        
        # 分析门控模块的参数
        gate1 = cross_attn.convnext_gate1
        gate2 = cross_attn.convnext_gate2
        
        print(f"\n门控模块参数:")
        print(f"gate1.fusion_weight: {gate1.fusion_weight.item():.6f}")
        print(f"gate2.fusion_weight: {gate2.fusion_weight.item():.6f}")

def propose_improvements():
    """提出改进建议"""
    print("\n=== 改进建议 ===")
    
    print("问题分析:")
    print("1. fusion_weight初始值0.1太小")
    print("2. 门控权重可能偏小（Sigmoid输出范围[0,1]，但实际可能集中在较小值）")
    print("3. ConvNeXt特征投影后的幅度可能与原特征不匹配")
    
    print("\n改进方案:")
    print("1. 增大fusion_weight初始值（建议0.3-0.5）")
    print("2. 调整门控网络结构，增加输出权重")
    print("3. 添加特征归一化，确保ConvNeXt特征与原特征在相同尺度")
    print("4. 使用可学习的缩放因子")

def test_improved_fusion_gate():
    """测试改进后的融合门控"""
    print("\n=== 测试改进后的融合门控 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    num_heads = 8
    batch_size = 2
    H, W = 16, 16
    N = H * W
    
    # 创建测试数据
    original_feature = torch.randn(batch_size, N, dim).to(device)
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
    
    # 创建改进的门控模块
    gate = ConvNeXtFusionGate(dim, num_heads).to(device)
    
    # 改进1: 增大fusion_weight
    gate.fusion_weight.data = torch.tensor(0.5)
    
    with torch.no_grad():
        enhanced_feature = gate(original_feature, convnext_feature)
        
        # 计算影响
        absolute_change = torch.abs(enhanced_feature - original_feature).mean().item()
        relative_change = (absolute_change / torch.abs(original_feature).mean()).item()
        
        print(f"改进后的影响:")
        print(f"平均绝对变化: {absolute_change:.6f}")
        print(f"相对变化: {relative_change:.6f}")
        
        # 与原始版本对比
        gate_orig = ConvNeXtFusionGate(dim, num_heads).to(device)
        enhanced_feature_orig = gate_orig(original_feature, convnext_feature)
        absolute_change_orig = torch.abs(enhanced_feature_orig - original_feature).mean().item()
        
        print(f"\n对比:")
        print(f"原始版本影响: {absolute_change_orig:.6f}")
        print(f"改进版本影响: {absolute_change:.6f}")
        print(f"改进倍数: {absolute_change / absolute_change_orig:.2f}x")

def main():
    """主调试函数"""
    print("ConvNeXt特征影响度调试分析")
    print("="*50)
    
    debug_fusion_gate()
    test_different_fusion_weights()
    analyze_cross_attention_impact()
    propose_improvements()
    test_improved_fusion_gate()
    
    print("\n" + "="*50)
    print("调试分析完成！")

if __name__ == "__main__":
    main()
