import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

from timm.models.layers import trunc_normal_, DropPath


# FAMNet中的注意力匹配模块
class AttentionMatching(nn.Module):
    def __init__(self, feature_dim=512, seq_len=5000):
        super(AttentionMatching, self).__init__()
        self.fc_spt = nn.Sequential(
            nn.Linear(seq_len, seq_len // 10),
            nn.ReLU(),
            nn.Linear(seq_len // 10, seq_len),
        )
        self.fc_qry = nn.Sequential(
            nn.Linear(seq_len, seq_len // 10),
            nn.ReLU(),
            nn.Linear(seq_len // 10, seq_len),
        )
        self.fc_fusion = nn.Sequential(
            nn.Linear(seq_len * 2, seq_len // 5),
            nn.ReLU(),
            nn.Linear(seq_len // 5, 2 * seq_len),
        )
        self.sigmoid = nn.Sigmoid()

    def correlation_matrix(self, spt_fg_fts, qry_fg_fts):
        spt_fg_fts = F.normalize(spt_fg_fts, p=2, dim=1)
        qry_fg_fts = F.normalize(qry_fg_fts, p=2, dim=1)
        cosine_similarity = torch.sum(spt_fg_fts * qry_fg_fts, dim=1, keepdim=True)
        return cosine_similarity

    def forward(self, spt_fg_fts, qry_fg_fts, band):
        spt_proj = F.relu(self.fc_spt(spt_fg_fts), inplace=False)
        qry_proj = F.relu(self.fc_qry(qry_fg_fts), inplace=False)

        similarity_matrix = self.sigmoid(self.correlation_matrix(spt_fg_fts, qry_fg_fts))

        if band == 'low' or band == 'high':
            weighted_spt = (1 - similarity_matrix) * spt_proj
            weighted_qry = (1 - similarity_matrix) * qry_proj
        else:
            weighted_spt = similarity_matrix * spt_proj
            weighted_qry = similarity_matrix * qry_proj

        combined = torch.cat((weighted_spt, weighted_qry), dim=2)
        fused_tensor = F.relu(self.fc_fusion(combined), inplace=False)

        return fused_tensor


# FAMNet中的频域分析模块
class FAM(nn.Module):
    def __init__(self, feature_dim=512, N=900):
        super(FAM, self).__init__()
        # 不在初始化时设置设备，让PyTorch自动处理
        self.attention_matching = AttentionMatching(feature_dim, N)
        self.adapt_pooling = nn.AdaptiveAvgPool1d(N)

    def forward(self, spt_fg_fts, qry_fg_fts):
        # 适配输入格式
        if isinstance(spt_fg_fts, list) and len(spt_fg_fts) > 0:
            if isinstance(spt_fg_fts[0], list):
                spt_fg_fts = spt_fg_fts[0][0]  # 取第一个way第一个shot
            else:
                spt_fg_fts = spt_fg_fts[0]

        if isinstance(qry_fg_fts, list):
            qry_fg_fts = qry_fg_fts[0] if len(qry_fg_fts) > 0 else qry_fg_fts

        # 确保输入是正确的张量格式
        if qry_fg_fts.shape[2] == 0:
            qry_fg_fts = F.pad(qry_fg_fts, (0, 1))

        spt_fg_fts = self.adapt_pooling(spt_fg_fts)
        qry_fg_fts = self.adapt_pooling(qry_fg_fts)

        spt_fg_fts_low, spt_fg_fts_mid, spt_fg_fts_high = self.filter_frequency_bands(spt_fg_fts, cutoff=0.30)
        qry_fg_fts_low, qry_fg_fts_mid, qry_fg_fts_high = self.filter_frequency_bands(qry_fg_fts, cutoff=0.30)

        fused_fts_low = self.attention_matching(spt_fg_fts_low, qry_fg_fts_low, 'low')
        fused_fts_mid = self.attention_matching(spt_fg_fts_mid, qry_fg_fts_mid, 'mid')
        fused_fts_high = self.attention_matching(spt_fg_fts_high, qry_fg_fts_high, 'high')

        return fused_fts_low, fused_fts_mid, fused_fts_high

    def reshape_to_square(self, tensor):
        B, C, N = tensor.shape
        side_length = int(np.ceil(np.sqrt(N)))
        padded_length = side_length ** 2

        padded_tensor = torch.zeros((B, C, padded_length), device=tensor.device)
        padded_tensor[:, :, :N] = tensor

        square_tensor = padded_tensor.view(B, C, side_length, side_length)

        return square_tensor, side_length, side_length, N

    def filter_frequency_bands(self, tensor, cutoff=0.2):
        tensor = tensor.float()
        tensor, H, W, N = self.reshape_to_square(tensor)
        B, C, _, _ = tensor.shape

        max_radius = np.sqrt((H // 2)**2 + (W // 2)**2)
        low_cutoff = max_radius * cutoff
        high_cutoff = max_radius * (1 - cutoff)

        fft_tensor = torch.fft.fftshift(torch.fft.fft2(tensor, dim=(-2, -1)), dim=(-2, -1))

        def create_filter(shape, low_cutoff, high_cutoff, mode='band', device=None):
            if device is None:
                device = tensor.device  # 使用输入张量的设备
            rows, cols = shape
            center_row, center_col = rows // 2, cols // 2

            y, x = torch.meshgrid(torch.arange(rows, device=device), torch.arange(cols, device=device), indexing='ij')
            distance = torch.sqrt((y - center_row) ** 2 + (x - center_col) ** 2)

            mask = torch.zeros((rows, cols), dtype=torch.float32, device=device)

            if mode == 'low':
                mask[distance <= low_cutoff] = 1
            elif mode == 'high':
                mask[distance >= high_cutoff] = 1
            elif mode == 'band':
                mask[(distance > low_cutoff) & (distance < high_cutoff)] = 1

            return mask

        low_pass_filter = create_filter((H, W), low_cutoff, None, mode='low', device=tensor.device)[None, None, :, :]
        high_pass_filter = create_filter((H, W), None, high_cutoff, mode='high', device=tensor.device)[None, None, :, :]
        mid_pass_filter = create_filter((H, W), low_cutoff, high_cutoff, mode='band', device=tensor.device)[None, None, :, :]

        low_freq_fft = fft_tensor * low_pass_filter
        high_freq_fft = fft_tensor * high_pass_filter
        mid_freq_fft = fft_tensor * mid_pass_filter

        low_freq_tensor = torch.fft.ifft2(torch.fft.ifftshift(low_freq_fft, dim=(-2, -1)), dim=(-2, -1)).real
        high_freq_tensor = torch.fft.ifft2(torch.fft.ifftshift(high_freq_fft, dim=(-2, -1)), dim=(-2, -1)).real
        mid_freq_tensor = torch.fft.ifft2(torch.fft.ifftshift(mid_freq_fft, dim=(-2, -1)), dim=(-2, -1)).real

        low_freq_tensor = low_freq_tensor.view(B, C, H * W)[:, :, :N]
        high_freq_tensor = high_freq_tensor.view(B, C, H * W)[:, :, :N]
        mid_freq_tensor = mid_freq_tensor.view(B, C, H * W)[:, :, :N]

        return low_freq_tensor, mid_freq_tensor, high_freq_tensor


# FAMNet中的多尺度特征融合模块
class MSFM(nn.Module):
    def __init__(self, feature_dim):
        super(MSFM, self).__init__()
        self.CA1 = CrossAttentionFusion(feature_dim)
        self.CA2 = CrossAttentionFusion(feature_dim)
        self.relu = nn.ReLU(inplace=False)

    def forward(self, low, mid, high):
        low_new = self.CA1(mid, low)
        high_new = self.CA2(mid, high)
        fused_features = self.relu(low_new + mid + high_new)
        return fused_features


# 基于FAMNet的频域分解和融合的CrossAttention
class CrossAttentionFusion(nn.Module):
    def __init__(self, embed_dim):
        super(CrossAttentionFusion, self).__init__()
        self.query = nn.Linear(embed_dim, embed_dim)
        self.key = nn.Linear(embed_dim, embed_dim)
        self.value = nn.Linear(embed_dim, embed_dim)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, Q_feature, K_feature):
        _, C, _ = Q_feature.shape

        Q_feature = Q_feature.permute(0, 2, 1)  # (B, N, C)
        K_feature = K_feature.permute(0, 2, 1)  # (B, N, C)

        Q = self.query(Q_feature)  # shape: [B, N, C]
        K = self.key(K_feature)    # shape: [B, N, C]
        V = self.value(K_feature)  # shape: [B, N, C]

        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / torch.sqrt(torch.tensor(C, dtype=torch.float32))
        attention_weights = self.softmax(attention_scores)  # shape: [B, N, N]

        attended_features = torch.matmul(attention_weights, V)  # shape: [B, N, C]
        attended_features = attended_features.permute(0, 2, 1)  # (B, C, N)

        return attended_features


# ConvNeXt特征融合门控模块
class ConvNeXtFusionGate(nn.Module):
    """
    ConvNeXt特征融合门控模块
    用于控制ConvNeXt特征对全局注意力分数的贡献度
    """
    def __init__(self, dim, num_heads):
        super(ConvNeXtFusionGate, self).__init__()
        self.dim = dim
        self.num_heads = num_heads
        head_dim = dim // num_heads

        # ConvNeXt特征投影层
        self.convnext_proj = nn.Linear(dim, dim, bias=False)

        # 门控权重生成网络
        self.gate_net = nn.Sequential(
            nn.Linear(dim * 2, dim // 4),  # 输入原特征和ConvNeXt特征的拼接
            nn.ReLU(inplace=False),
            nn.Linear(dim // 4, 1),
            nn.Sigmoid()
        )

        # 自适应融合权重
        self.fusion_weight = nn.Parameter(torch.tensor(0.1))  # 初始化为较小值，保持原ctx主导

    def forward(self, original_feature, convnext_feature):
        """
        Args:
            original_feature: 原始特征 (B, N, C)
            convnext_feature: ConvNeXt特征 (B, C, H, W)
        Returns:
            融合后的特征 (B, N, C)
        """
        B, N, C = original_feature.shape

        # 将ConvNeXt特征转换为序列格式
        if len(convnext_feature.shape) == 4:  # (B, C, H, W)
            _, _, H, W = convnext_feature.shape
            convnext_seq = convnext_feature.flatten(2).transpose(1, 2)  # (B, H*W, C)

            # 如果尺寸不匹配，使用自适应池化调整
            if convnext_seq.shape[1] != N:
                convnext_seq = F.adaptive_avg_pool1d(
                    convnext_seq.permute(0, 2, 1), N
                ).permute(0, 2, 1)  # (B, N, C)
        else:
            convnext_seq = convnext_feature

        # 投影ConvNeXt特征到相同维度
        convnext_proj = self.convnext_proj(convnext_seq)  # (B, N, C)

        # 计算门控权重
        combined_features = torch.cat([original_feature, convnext_proj], dim=-1)  # (B, N, 2C)
        gate_weights = self.gate_net(combined_features)  # (B, N, 1)

        # 自适应融合：原特征为主，ConvNeXt特征为辅
        # 使用残差连接和门控机制
        enhanced_feature = original_feature + self.fusion_weight * gate_weights * convnext_proj

        return enhanced_feature


# 论文图示里的阶段1，跨注意力机制 - 增强版本，支持ConvNeXt特征融合
class CrossAttention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, qk_scale=None, enable_convnext_fusion=True):
        super(CrossAttention, self).__init__()
        assert dim % num_heads == 0, f"dim {dim} should be divided by num_heads {num_heads}."

        self.dim = dim
        self.num_heads = num_heads
        self.enable_convnext_fusion = enable_convnext_fusion
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5   # 缩放因子
        self.kv1 = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.kv2 = nn.Linear(dim, dim * 2, bias=qkv_bias)

        # ConvNeXt特征融合门控模块
        if self.enable_convnext_fusion:
            self.convnext_gate1 = ConvNeXtFusionGate(dim, num_heads)
            self.convnext_gate2 = ConvNeXtFusionGate(dim, num_heads)

    def forward(self, x1, x2, convnext_feature=None):
        """
        Args:
            x1, x2: 输入特征 (B, N, C)
            convnext_feature: ConvNeXt特征 (B, C, H, W) 或 None
        Returns:
            融合后的特征 (x1_out, x2_out)
        """
        B, N, C = x1.shape  # B的形状为(B,N,C)，N为dim
        q1 = x1.reshape(B, -1, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3).contiguous()
        q2 = x2.reshape(B, -1, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3).contiguous()
        # q1与q2的形状为(B,num_heads,N,head_dim)
        k1, v1 = self.kv1(x1).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4).contiguous()
        k2, v2 = self.kv2(x2).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4).contiguous()
        # 形状为(B,num_heads,N,head_dim)

        # 计算原始的全局注意力分数
        ctx1_original = (k1.transpose(-2, -1) @ v1) * self.scale # 形状为(B,num_head,head_dim,head_dim)
        ctx1_original = ctx1_original.softmax(dim=-2)
        ctx2_original = (k2.transpose(-2, -1) @ v2) * self.scale
        ctx2_original = ctx2_original.softmax(dim=-2)

        # 如果提供了ConvNeXt特征，则进行融合
        if self.enable_convnext_fusion and convnext_feature is not None:
            # 简化融合策略：直接在原始特征x1, x2上融合ConvNeXt信息
            # 将ConvNeXt特征转换为序列格式
            if len(convnext_feature.shape) == 4:  # (B, C, H, W)
                convnext_seq = convnext_feature.flatten(2).transpose(1, 2)  # (B, H*W, C)
                # 如果尺寸不匹配，使用自适应池化调整
                if convnext_seq.shape[1] != N:
                    convnext_seq = F.adaptive_avg_pool1d(
                        convnext_seq.permute(0, 2, 1), N
                    ).permute(0, 2, 1)  # (B, N, C)
            else:
                convnext_seq = convnext_feature

            # 使用门控机制增强原始特征
            x1_enhanced = self.convnext_gate1(x1, convnext_seq)
            x2_enhanced = self.convnext_gate2(x2, convnext_seq)

            # 重新计算增强后的k, v
            k1_enhanced, v1_enhanced = self.kv1(x1_enhanced).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4).contiguous()
            k2_enhanced, v2_enhanced = self.kv2(x2_enhanced).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4).contiguous()

            # 计算增强后的全局注意力分数
            ctx1 = (k1_enhanced.transpose(-2, -1) @ v1_enhanced) * self.scale
            ctx1 = ctx1.softmax(dim=-2)
            ctx2 = (k2_enhanced.transpose(-2, -1) @ v2_enhanced) * self.scale
            ctx2 = ctx2.softmax(dim=-2)
        else:
            ctx1 = ctx1_original
            ctx2 = ctx2_original

        x1 = (q1 @ ctx2).permute(0, 2, 1, 3).reshape(B, N, C).contiguous()  # 矩阵乘完形状为(B,num_heads,N,head_dim)，permute之后再reshape为(B,N,C)
        x2 = (q2 @ ctx1).permute(0, 2, 1, 3).reshape(B, N, C).contiguous()

        return x1, x2


class CrossPath(nn.Module):
    def __init__(self, dim, reduction=1, num_heads=None, norm_layer=nn.LayerNorm, enable_convnext_fusion=True):
        super().__init__()
        self.channel_proj1 = nn.Linear(dim, dim // reduction * 2)
        self.channel_proj2 = nn.Linear(dim, dim // reduction * 2)
        self.act1 = nn.ReLU(inplace=False)
        self.act2 = nn.ReLU(inplace=False)
        self.cross_attn = CrossAttention(dim // reduction, num_heads=num_heads, enable_convnext_fusion=enable_convnext_fusion)
        self.end_proj1 = nn.Linear(dim // reduction * 2, dim)
        self.end_proj2 = nn.Linear(dim // reduction * 2, dim)
        self.norm1 = norm_layer(dim)
        self.norm2 = norm_layer(dim)

    def forward(self, x1, x2, convnext_feature=None):
        y1, u1 = self.act1(self.channel_proj1(x1)).chunk(2, dim=-1) # y1与u1的形状一模一样，均为(B,N,C)
        y2, u2 = self.act2(self.channel_proj2(x2)).chunk(2, dim=-1)
        v1, v2 = self.cross_attn(u1, u2, convnext_feature)
        y1 = torch.cat((y1, v1), dim=-1)    # 形状为(B,N,2C)
        y2 = torch.cat((y2, v2), dim=-1)
        out_x1 = self.norm1(x1 + self.end_proj1(y1))    # 跳转连接，形状为(B,N,C)
        out_x2 = self.norm2(x2 + self.end_proj2(y2))

        return out_x1, out_x2


# Dynamic LWGA 通道分割模块
class DynamicChannelSplit(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.fc = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(dim, dim//4, 1),
            nn.GELU(),
            nn.Conv2d(dim//4, 4, 1),
        )

    def forward(self, x):
        weight = self.fc(x)  # (B,4,1,1)
        weight = F.softmax(weight.squeeze(-1).squeeze(-1), dim=1)  # (B,4)
        return weight

# 简单注意力分支
class SimpleAttentionBranch(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(dim, dim, 3, 1, 1, groups=dim),
            nn.BatchNorm2d(dim),
            nn.GELU(),
            nn.Conv2d(dim, dim, 1)
        )

    def forward(self, x):
        return self.conv(x)

# 基于Dynamic LWGA的通道嵌入层 - 替代原有的SENet机制
class ChannelEmbed(nn.Module):
    def __init__(self, in_channels, out_channels, mlp_ratio=4.0, drop_path=0.1, norm_layer=nn.BatchNorm2d):
        super(ChannelEmbed, self).__init__()
        self.out_channels = out_channels

        # 输入通道调整层
        self.input_conv = nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False)

        # Dynamic LWGA 组件
        self.splitter = DynamicChannelSplit(out_channels)

        # 四个注意力分支 - 对应PA, LA, MRA, GA
        self.branch_pa = SimpleAttentionBranch(out_channels)    # Position Attention
        self.branch_la = SimpleAttentionBranch(out_channels)    # Local Attention
        self.branch_mra = SimpleAttentionBranch(out_channels)   # Multi-Resolution Attention
        self.branch_ga = SimpleAttentionBranch(out_channels)    # Global Attention

        # MLP融合层
        mlp_hidden_dim = int(out_channels * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Conv2d(out_channels, mlp_hidden_dim, 1),
            nn.GELU(),
            nn.Conv2d(mlp_hidden_dim, out_channels, 1)
        )

        # 归一化和DropPath
        self.norm = norm_layer(out_channels)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x, H, W):
        B, _, _C = x.shape
        # 重新调整为(B,_C,H,W)的形状，使其符合卷积层的输入要求
        x = x.permute(0, 2, 1).reshape(B, _C, H, W).contiguous()

        # 输入通道调整
        x = self.input_conv(x)  # (B, out_channels, H, W)

        # 保存残差连接的输入
        shortcut = x

        # === Dynamic LWGA 注意力机制 ===
        # 动态权重分配
        weights = self.splitter(x)  # (B, 4)
        B, C, H, W = x.shape

        # 将输入复制四份，每份乘以对应权重
        x1 = x * weights[:, 0].view(B, 1, 1, 1)  # Position Attention 分支
        x2 = x * weights[:, 1].view(B, 1, 1, 1)  # Local Attention 分支
        x3 = x * weights[:, 2].view(B, 1, 1, 1)  # Multi-Resolution Attention 分支
        x4 = x * weights[:, 3].view(B, 1, 1, 1)  # Global Attention 分支

        # 各分支独立处理
        x1 = self.branch_pa(x1)
        x2 = self.branch_la(x2)
        x3 = self.branch_mra(x3)
        x4 = self.branch_ga(x4)

        # 融合四个分支的输出
        x_att = x1 + x2 + x3 + x4

        # MLP融合和残差连接
        output = shortcut + self.drop_path(self.mlp(self.norm(x_att)))

        return output


# 形状适配器 - 将(B,H*W,C)转换为FAM需要的(B,C,N)格式
class ShapeAdapter(nn.Module):
    def __init__(self, dim):
        super(ShapeAdapter, self).__init__()
        self.dim = dim

    def to_fam_format(self, x):
        """将(B,H*W,C)转换为(B,C,H*W)"""
        B, N, C = x.shape
        return x.permute(0, 2, 1).contiguous()  # (B,C,N)

    def from_fam_format(self, x):
        """将(B,C,N)转换为(B,N,C)"""
        B, C, N = x.shape
        return x.permute(0, 2, 1).contiguous()  # (B,N,C)


# 改进的MSF频域-空域融合层
class ImprovedMSFFusion(nn.Module):
    def __init__(self, dim, norm_layer=nn.BatchNorm2d):
        super(ImprovedMSFFusion, self).__init__()
        self.dim = dim

        # 通道调整层 - 将空域特征从2C调整到C，使其与频域特征维度一致
        self.spatial_channel_adjust = nn.Sequential(
            nn.Conv2d(dim * 2, dim, kernel_size=1, bias=False),
            norm_layer(dim),
            nn.ReLU(inplace=False)
        )

        # 全局平均池化
        self.gap = nn.AdaptiveAvgPool2d(1)

        # SE模块 - 分别为频域和空域特征建模通道相关性
        self.freq_se = nn.Sequential(
            nn.Conv2d(dim, dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=False)
        )

        self.spatial_se = nn.Sequential(
            nn.Conv2d(dim, dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=False)
        )

        # 门控机制
        self.sigmoid = nn.Sigmoid()
        self.softmax = nn.Softmax(dim=2)

        # 输出投影层 - 增强特征表达能力
        self.output_proj = nn.Sequential(
            nn.Conv2d(dim, dim * 2, kernel_size=3, padding=1, bias=False),
            norm_layer(dim * 2),
            nn.ReLU(inplace=False),
            nn.Conv2d(dim * 2, dim * 2, kernel_size=1, bias=False),
            norm_layer(dim * 2)
        )

        # 残差连接投影层
        self.residual_proj = nn.Sequential(
            nn.Conv2d(dim, dim * 2, kernel_size=1, bias=False),
            norm_layer(dim * 2)
        )

    def forward(self, freq_features, spatial_features, H, W):
        """
        Args:
            freq_features: 频域融合特征 (B, N, C)
            spatial_features: 空域融合特征 (B, N, 2C)
            H, W: 原始空间尺寸
        Returns:
            output: 融合后的特征 (B, 2C, H, W)
        """
        B, N, C = freq_features.shape
        _, _, spatial_C = spatial_features.shape

        # 检查并调整频域特征的维度以匹配空间尺寸
        if freq_features.shape[1] != H * W:
            freq_features = F.adaptive_avg_pool1d(freq_features.permute(0, 2, 1), H * W).permute(0, 2, 1)

        # 转换为空间格式
        freq_spatial = freq_features.permute(0, 2, 1).reshape(B, C, H, W)  # (B, C, H, W)
        spatial_spatial = spatial_features.permute(0, 2, 1).reshape(B, spatial_C, H, W)  # (B, 2C, H, W)

        # 调整空域特征通道数以匹配频域特征
        spatial_adjusted = self.spatial_channel_adjust(spatial_spatial)  # (B, C, H, W)

        # === MSF门控融合机制 ===
        # 通过全局平均池化获取全局信息，然后通过SE模块建模通道相关性
        freq_weight = self.freq_se(self.gap(freq_spatial))  # (B, C, 1, 1)
        spatial_weight = self.spatial_se(self.gap(spatial_adjusted))  # (B, C, 1, 1)

        # 将两个特征的权重进行拼接: (B, C, 2, 1)
        weight = torch.cat([freq_weight, spatial_weight], dim=2)  # (B, C, 2, 1)

        # 通过sigmoid和softmax获得归一化权重
        weight = self.softmax(self.sigmoid(weight))  # (B, C, 2, 1)

        # 分离权重
        freq_gate = weight[:, :, 0:1, :]  # (B, C, 1, 1)
        spatial_gate = weight[:, :, 1:2, :]  # (B, C, 1, 1)

        # 加权融合
        fused_features = freq_gate * freq_spatial + spatial_gate * spatial_adjusted  # (B, C, H, W)

        # 残差连接
        residual = self.residual_proj(fused_features)  # (B, 2C, H, W)

        # 输出投影
        output = self.output_proj(fused_features)  # (B, 2C, H, W)

        # 残差连接
        final_output = residual + output  # (B, 2C, H, W)

        return final_output


# 整个FFM跨模态注意力类的实现 - 增强版本，包含频域处理和ConvNeXt融合
class FeatureFusionModule(nn.Module):
    def __init__(self, dim, reduction=1, num_heads=8, norm_layer=nn.BatchNorm2d, mlp_ratio=4.0, drop_path=0.1, enable_convnext_fusion=True):
        super().__init__()
        self.dim = dim
        self.enable_convnext_fusion = enable_convnext_fusion

        # 原有的空域处理分支 - 现在支持ConvNeXt特征融合
        self.cross = CrossPath(dim=dim, reduction=reduction, num_heads=num_heads, enable_convnext_fusion=enable_convnext_fusion)

        # 新增的频域处理分支
        # 计算序列长度N，假设输入是16x16的特征图
        self.N = 256  # 默认值，会在forward中动态调整

        # 两个并行的FAM层分别处理两个模态
        self.fam_x1 = FAM(feature_dim=dim, N=self.N)
        self.fam_x2 = FAM(feature_dim=dim, N=self.N)

        # 两个MSFM层分别融合各自的频域特征
        self.msfm_x1 = MSFM(feature_dim=dim)
        self.msfm_x2 = MSFM(feature_dim=dim)

        # 形状适配器
        self.shape_adapter = ShapeAdapter(dim)

        # 频域特征拼接后的门控融合机制
        self.freq_gate = nn.Sequential(
            nn.Conv1d(dim * 2, dim, kernel_size=1, bias=False),
            nn.BatchNorm1d(dim),
            nn.ReLU(inplace=False)
        )

        # 频域-空域融合层 - 使用改进的MSF融合机制
        self.freq_spatial_fusion = ImprovedMSFFusion(dim, norm_layer)

        # 最终的通道嵌入层 - 使用Dynamic LWGA注意力机制
        self.channel_emb = ChannelEmbed(in_channels=dim*2, out_channels=dim, mlp_ratio=mlp_ratio, drop_path=drop_path, norm_layer=norm_layer)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x1, x2, convnext_feature=None):
        """
        Args:
            x1, x2: RGB和Depth特征 (B, C, H, W)
            convnext_feature: ConvNeXt特征 (B, C, H, W) 或 None
        Returns:
            融合后的特征 (B, C, H, W)
        """
        B, C, H, W = x1.shape
        N = H * W

        # 转换为序列格式 (B,H*W,C)
        x1_seq = x1.flatten(2).transpose(1, 2)
        x2_seq = x2.flatten(2).transpose(1, 2)

        # === 空域处理分支 (现在支持ConvNeXt特征融合) ===
        if self.enable_convnext_fusion and convnext_feature is not None:
            x1_spatial, x2_spatial = self.cross(x1_seq, x2_seq, convnext_feature)  # (B,N,C)
        else:
            x1_spatial, x2_spatial = self.cross(x1_seq, x2_seq)  # (B,N,C)
        spatial_merge = torch.cat((x1_spatial, x2_spatial), dim=-1)  # (B,N,2C)

        # === 频域处理分支 (新增逻辑) ===
        # 转换为FAM需要的格式 (B,C,N)
        x1_fam = self.shape_adapter.to_fam_format(x1_seq)  # (B,C,N)
        x2_fam = self.shape_adapter.to_fam_format(x2_seq)  # (B,C,N)

        # 并行FAM处理 - 分别处理两个模态
        try:
            # 使用自适应池化调整到FAM期望的序列长度
            if x1_fam.shape[2] != self.N:
                x1_fam = F.adaptive_avg_pool1d(x1_fam, self.N)
            if x2_fam.shape[2] != self.N:
                x2_fam = F.adaptive_avg_pool1d(x2_fam, self.N)

            # 为FAM准备输入格式 - 需要模拟support和query的格式
            x1_low, x1_mid, x1_high = self.fam_x1([x1_fam], [x1_fam])
            x2_low, x2_mid, x2_high = self.fam_x2([x2_fam], [x2_fam])

            # MSFM融合各自的频域特征
            x1_freq_fused = self.msfm_x1(x1_low, x1_mid, x1_high)  # (B,C,N)
            x2_freq_fused = self.msfm_x2(x2_low, x2_mid, x2_high)  # (B,C,N)

            # 拼接两个模态的频域特征
            freq_concat = torch.cat([x1_freq_fused, x2_freq_fused], dim=1)  # (B, 2C, N)

            # 通过门控机制直接融合拼接后的特征
            freq_combined = self.freq_gate(freq_concat)  # (B, C, N)

            # 如果需要，调整回原始序列长度
            if freq_combined.shape[2] != H * W:
                freq_combined = F.adaptive_avg_pool1d(freq_combined, H * W)

            # 转换回序列格式
            freq_merge = self.shape_adapter.from_fam_format(freq_combined)  # (B,N,C)

        except Exception as e:
            # 如果频域处理失败，使用空域结果作为备选
            print(f"频域处理失败，使用空域结果: {e}")
            freq_merge = torch.mean(torch.stack([x1_spatial, x2_spatial]), dim=0)  # (B,N,C)

        # === 频域-空域融合 ===
        # 使用融合卷积层将频域和空域特征结合
        final_features = self.freq_spatial_fusion(freq_merge, spatial_merge, H, W)  # (B,2C,H,W)

        # 转换回序列格式用于ChannelEmbed
        final_seq = final_features.flatten(2).transpose(1, 2)  # (B,H*W,2C)

        # 最终的通道嵌入
        output = self.channel_emb(final_seq, H, W)

        return output

# if __name__ == "__main__":
#     print("=== 测试增强版FFM模块（包含频域处理）===")

#     # 检查设备
#     device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
#     print(f"使用设备: {device}")

#     # 测试参数
#     dim = 48
#     batch_size = 2
#     H, W = 16, 16

#     # 创建测试数据
#     x1 = torch.randn(batch_size, dim, H, W).to(device)
#     x2 = torch.randn(batch_size, dim, H, W).to(device)

#     print(f"输入x1形状: {x1.shape}")
#     print(f"输入x2形状: {x2.shape}")

#     try:
#         # 测试增强版FeatureFusionModule
#         print("\n=== 测试增强版FeatureFusionModule ===")
#         ffm = FeatureFusionModule(dim=dim).to(device)
#         out = ffm(x1, x2)
#         print(f"增强版FFM输出形状: {out.shape}")
#         print("✓ 增强版FeatureFusionModule测试成功！")

#         # 测试FAM模块单独使用
#         print("\n=== 测试FAM模块 ===")
#         N = H * W
#         fam = FAM(feature_dim=dim, N=N).to(device)

#         # 准备FAM输入格式
#         x1_fam = x1.flatten(2).transpose(1, 2).permute(0, 2, 1)  # (B, C, N)
#         x2_fam = x2.flatten(2).transpose(1, 2).permute(0, 2, 1)  # (B, C, N)

#         low, mid, high = fam([x1_fam], [x2_fam])
#         print(f"FAM低频输出形状: {low.shape}")
#         print(f"FAM中频输出形状: {mid.shape}")
#         print(f"FAM高频输出形状: {high.shape}")
#         print("✓ FAM测试成功！")

#         # 测试MSFM模块
#         print("\n=== 测试MSFM模块 ===")
#         msfm = MSFM(feature_dim=dim).to(device)
#         fused = msfm(low, mid, high)
#         print(f"MSFM融合输出形状: {fused.shape}")
#         print("✓ MSFM测试成功！")

#         # 测试形状适配器
#         print("\n=== 测试ShapeAdapter ===")
#         adapter = ShapeAdapter(dim)
#         x1_seq = x1.flatten(2).transpose(1, 2)  # (B, H*W, C)
#         x1_fam_format = adapter.to_fam_format(x1_seq)  # (B, C, N)
#         x1_back = adapter.from_fam_format(x1_fam_format)  # (B, N, C)
#         print(f"原始序列形状: {x1_seq.shape}")
#         print(f"FAM格式形状: {x1_fam_format.shape}")
#         print(f"转换回序列形状: {x1_back.shape}")
#         print("✓ ShapeAdapter测试成功！")

#         # 测试改进的MSF频域-空域融合层
#         print("\n=== 测试ImprovedMSFFusion ===")
#         fusion = ImprovedMSFFusion(dim).to(device)
#         freq_features = torch.randn(batch_size, H*W, dim).to(device)  # (B, N, C)
#         spatial_features = torch.randn(batch_size, H*W, dim*2).to(device)  # (B, N, 2C)
#         fused_output = fusion(freq_features, spatial_features, H, W)
#         print(f"频域特征形状: {freq_features.shape}")
#         print(f"空域特征形状: {spatial_features.shape}")
#         print(f"融合输出形状: {fused_output.shape}")
#         print("✓ ImprovedMSFFusion测试成功！")

#         print("\n=== 所有增强功能测试通过！ ===")

#     except Exception as e:
#         print(f"✗ 测试失败: {e}")
#         import traceback
#         traceback.print_exc()