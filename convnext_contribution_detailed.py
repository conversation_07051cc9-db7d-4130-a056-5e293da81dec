#!/usr/bin/env python3
"""
ConvNeXt特征贡献度详细分析脚本
提供更深入的贡献度分析和可视化
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os
from typing import Dict, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.FFM_change import FeatureFusionModule

def analyze_convnext_contribution_detailed():
    """详细分析ConvNeXt特征贡献度"""
    print("="*70)
    print("ConvNeXt特征贡献度详细分析报告")
    print("="*70)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 测试参数
    dim = 96
    batch_size = 4
    H, W = 32, 32
    
    # 创建测试数据
    rgb_features = torch.randn(batch_size, dim, H, W).to(device)
    depth_features = torch.randn(batch_size, dim, H, W).to(device)
    convnext_features = torch.randn(batch_size, dim, H, W).to(device)
    
    print(f"\n📋 测试配置:")
    print(f"  特征维度: {dim}")
    print(f"  批次大小: {batch_size}")
    print(f"  特征图尺寸: {H}x{W}")
    print(f"  总像素数: {H*W}")
    print(f"  总参数数: {batch_size * dim * H * W}")
    
    # 创建FFM模块
    ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(device)
    
    with torch.no_grad():
        # 不使用ConvNeXt特征
        features_without = ffm(rgb_features, depth_features, None)
        
        # 使用ConvNeXt特征
        features_with = ffm(rgb_features, depth_features, convnext_features)
        
        print(f"\n📊 输入特征统计:")
        print(f"  RGB特征: 均值={rgb_features.mean():.6f}, 标准差={rgb_features.std():.6f}, 范围=[{rgb_features.min():.3f}, {rgb_features.max():.3f}]")
        print(f"  Depth特征: 均值={depth_features.mean():.6f}, 标准差={depth_features.std():.6f}, 范围=[{depth_features.min():.3f}, {depth_features.max():.3f}]")
        print(f"  ConvNeXt特征: 均值={convnext_features.mean():.6f}, 标准差={convnext_features.std():.6f}, 范围=[{convnext_features.min():.3f}, {convnext_features.max():.3f}]")
        
        print(f"\n📈 输出特征统计:")
        print(f"  不使用ConvNeXt: 均值={features_without.mean():.6f}, 标准差={features_without.std():.6f}, 范围=[{features_without.min():.3f}, {features_without.max():.3f}]")
        print(f"  使用ConvNeXt: 均值={features_with.mean():.6f}, 标准差={features_with.std():.6f}, 范围=[{features_with.min():.3f}, {features_with.max():.3f}]")
        
        # 计算差异
        abs_diff = torch.abs(features_with - features_without)
        rel_diff = abs_diff / (torch.abs(features_without) + 1e-8)
        
        print(f"\n🔍 ConvNeXt贡献度分析:")
        
        # 1. 基本贡献度指标
        l2_contribution = (torch.norm(abs_diff) / torch.norm(features_without) * 100).item()
        mean_contribution = (abs_diff.mean() / torch.abs(features_without).mean() * 100).item()
        max_contribution = (abs_diff.max() / torch.abs(features_without).max() * 100).item()
        
        print(f"  L2范数贡献百分比: {l2_contribution:.2f}%")
        print(f"  均值贡献百分比: {mean_contribution:.2f}%")
        print(f"  最大值贡献百分比: {max_contribution:.2f}%")
        
        # 2. 相似度指标
        cosine_sim = nn.functional.cosine_similarity(
            features_without.flatten(1), features_with.flatten(1), dim=1
        ).mean().item()
        
        # 计算皮尔逊相关系数
        x_flat = features_without.flatten()
        y_flat = features_with.flatten()
        x_mean = x_flat.mean()
        y_mean = y_flat.mean()
        numerator = ((x_flat - x_mean) * (y_flat - y_mean)).sum()
        denominator = torch.sqrt(((x_flat - x_mean) ** 2).sum() * ((y_flat - y_mean) ** 2).sum())
        pearson_corr = (numerator / (denominator + 1e-8)).item()
        
        print(f"  余弦相似度: {cosine_sim:.6f}")
        print(f"  皮尔逊相关系数: {pearson_corr:.6f}")
        
        # 3. 影响范围分析
        thresholds = [0.01, 0.05, 0.1, 0.2, 0.5]
        print(f"\n📍 影响范围分析:")
        for threshold in thresholds:
            affected_ratio = (abs_diff > threshold).float().mean().item() * 100
            print(f"  变化>{threshold*100:.0f}%的像素比例: {affected_ratio:.2f}%")
        
        # 4. 通道级分析
        print(f"\n🔬 通道级贡献度分析:")
        channel_contributions = (abs_diff.mean(dim=(0, 2, 3)) / torch.abs(features_without).mean(dim=(0, 2, 3)) * 100).cpu().numpy()
        
        print(f"  通道贡献度统计:")
        print(f"    均值: {np.mean(channel_contributions):.2f}%")
        print(f"    标准差: {np.std(channel_contributions):.2f}%")
        print(f"    最大值: {np.max(channel_contributions):.2f}% (通道 {np.argmax(channel_contributions)})")
        print(f"    最小值: {np.min(channel_contributions):.2f}% (通道 {np.argmin(channel_contributions)})")
        
        # 找出贡献度最高和最低的通道
        top_channels = np.argsort(channel_contributions)[-5:][::-1]
        bottom_channels = np.argsort(channel_contributions)[:5]
        
        print(f"  贡献度最高的5个通道:")
        for i, ch in enumerate(top_channels):
            print(f"    第{i+1}名: 通道{ch}, 贡献度{channel_contributions[ch]:.2f}%")
        
        print(f"  贡献度最低的5个通道:")
        for i, ch in enumerate(bottom_channels):
            print(f"    第{i+1}名: 通道{ch}, 贡献度{channel_contributions[ch]:.2f}%")
        
        # 5. 空间级分析
        print(f"\n🗺️ 空间级贡献度分析:")
        spatial_contributions = (abs_diff.mean(dim=(0, 1)) / torch.abs(features_without).mean(dim=(0, 1)) * 100).cpu().numpy()
        
        print(f"  空间贡献度统计:")
        print(f"    均值: {np.mean(spatial_contributions):.2f}%")
        print(f"    标准差: {np.std(spatial_contributions):.2f}%")
        print(f"    最大值: {np.max(spatial_contributions):.2f}% (位置 {np.unravel_index(np.argmax(spatial_contributions), spatial_contributions.shape)})")
        print(f"    最小值: {np.min(spatial_contributions):.2f}% (位置 {np.unravel_index(np.argmin(spatial_contributions), spatial_contributions.shape)})")
        
        # 6. 分布分析
        print(f"\n📊 贡献度分布分析:")
        abs_diff_flat = abs_diff.flatten().cpu().numpy()
        rel_diff_flat = rel_diff.flatten().cpu().numpy()
        
        # 计算百分位数
        percentiles = [10, 25, 50, 75, 90, 95, 99]
        print(f"  绝对差异百分位数:")
        for p in percentiles:
            value = np.percentile(abs_diff_flat, p)
            print(f"    {p}%: {value:.6f}")
        
        print(f"  相对差异百分位数:")
        for p in percentiles:
            value = np.percentile(rel_diff_flat, p)
            print(f"    {p}%: {value:.6f}")
        
        # 7. 信噪比分析
        signal_power = torch.mean(features_without ** 2)
        noise_power = torch.mean(abs_diff ** 2)
        snr_db = 10 * torch.log10(signal_power / (noise_power + 1e-8))
        
        print(f"\n📡 信号质量分析:")
        print(f"  信噪比: {snr_db.item():.2f} dB")
        print(f"  信号功率: {signal_power.item():.6f}")
        print(f"  噪声功率: {noise_power.item():.6f}")
        
        # 8. 综合评估
        print(f"\n🎯 综合评估:")
        
        if l2_contribution > 20:
            contribution_level = "显著"
            contribution_emoji = "🔥"
        elif l2_contribution > 10:
            contribution_level = "中等"
            contribution_emoji = "⚡"
        elif l2_contribution > 5:
            contribution_level = "轻微"
            contribution_emoji = "💫"
        else:
            contribution_level = "微弱"
            contribution_emoji = "💧"
        
        print(f"  {contribution_emoji} ConvNeXt特征影响程度: {contribution_level} (L2贡献 {l2_contribution:.1f}%)")
        
        if cosine_sim > 0.95:
            consistency_level = "极高"
            consistency_emoji = "✅"
        elif cosine_sim > 0.90:
            consistency_level = "高"
            consistency_emoji = "✅"
        elif cosine_sim > 0.80:
            consistency_level = "中等"
            consistency_emoji = "⚠️"
        else:
            consistency_level = "低"
            consistency_emoji = "❌"
        
        print(f"  {consistency_emoji} 特征一致性: {consistency_level} (余弦相似度 {cosine_sim:.4f})")
        
        # 影响均匀性
        channel_std = np.std(channel_contributions)
        spatial_std = np.std(spatial_contributions)
        
        if channel_std < 2.0 and spatial_std < 2.0:
            uniformity_level = "均匀"
            uniformity_emoji = "🎯"
        elif channel_std < 5.0 and spatial_std < 5.0:
            uniformity_level = "较均匀"
            uniformity_emoji = "🎯"
        else:
            uniformity_level = "不均匀"
            uniformity_emoji = "⚠️"
        
        print(f"  {uniformity_emoji} 影响分布: {uniformity_level} (通道标准差 {channel_std:.2f}%, 空间标准差 {spatial_std:.2f}%)")
        
        return {
            'l2_contribution': l2_contribution,
            'mean_contribution': mean_contribution,
            'cosine_similarity': cosine_sim,
            'pearson_correlation': pearson_corr,
            'channel_contributions': channel_contributions,
            'spatial_contributions': spatial_contributions,
            'snr_db': snr_db.item()
        }

def test_different_scenarios():
    """测试不同场景下的ConvNeXt贡献度"""
    print(f"\n" + "="*70)
    print("不同场景下的ConvNeXt贡献度对比")
    print("="*70)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    scenarios = [
        {"name": "小特征图", "dim": 96, "H": 16, "W": 16},
        {"name": "中特征图", "dim": 96, "H": 32, "W": 32},
        {"name": "大特征图", "dim": 96, "H": 64, "W": 64},
        {"name": "低维特征", "dim": 48, "H": 32, "W": 32},
        {"name": "高维特征", "dim": 192, "H": 32, "W": 32},
    ]
    
    results = []
    
    for scenario in scenarios:
        print(f"\n🔍 测试场景: {scenario['name']}")
        print(f"  配置: dim={scenario['dim']}, H={scenario['H']}, W={scenario['W']}")
        
        dim, H, W = scenario['dim'], scenario['H'], scenario['W']
        batch_size = 2
        
        # 创建测试数据
        rgb_features = torch.randn(batch_size, dim, H, W).to(device)
        depth_features = torch.randn(batch_size, dim, H, W).to(device)
        convnext_features = torch.randn(batch_size, dim, H, W).to(device)
        
        # 创建FFM模块
        ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(device)
        
        with torch.no_grad():
            features_without = ffm(rgb_features, depth_features, None)
            features_with = ffm(rgb_features, depth_features, convnext_features)
            
            # 计算贡献度
            abs_diff = torch.abs(features_with - features_without)
            l2_contribution = (torch.norm(abs_diff) / torch.norm(features_without) * 100).item()
            mean_contribution = (abs_diff.mean() / torch.abs(features_without).mean() * 100).item()
            
            cosine_sim = nn.functional.cosine_similarity(
                features_without.flatten(1), features_with.flatten(1), dim=1
            ).mean().item()
            
            result = {
                'scenario': scenario['name'],
                'l2_contribution': l2_contribution,
                'mean_contribution': mean_contribution,
                'cosine_similarity': cosine_sim,
                'total_params': batch_size * dim * H * W
            }
            results.append(result)
            
            print(f"  L2贡献: {l2_contribution:.2f}%")
            print(f"  均值贡献: {mean_contribution:.2f}%")
            print(f"  余弦相似度: {cosine_sim:.4f}")
    
    # 对比分析
    print(f"\n📊 场景对比分析:")
    print(f"{'场景':<10} {'L2贡献%':<10} {'均值贡献%':<12} {'余弦相似度':<12} {'参数量':<10}")
    print("-" * 60)
    for result in results:
        print(f"{result['scenario']:<10} {result['l2_contribution']:<10.2f} {result['mean_contribution']:<12.2f} {result['cosine_similarity']:<12.4f} {result['total_params']:<10}")
    
    return results

def main():
    """主函数"""
    # 详细分析
    detailed_results = analyze_convnext_contribution_detailed()
    
    # 不同场景对比
    scenario_results = test_different_scenarios()
    
    print(f"\n" + "="*70)
    print("🎉 ConvNeXt特征贡献度分析完成！")
    print(f"主要发现:")
    print(f"  • ConvNeXt特征L2贡献度: {detailed_results['l2_contribution']:.2f}%")
    print(f"  • 特征一致性(余弦相似度): {detailed_results['cosine_similarity']:.4f}")
    print(f"  • 信噪比: {detailed_results['snr_db']:.2f} dB")
    print("="*70)

if __name__ == "__main__":
    main()
