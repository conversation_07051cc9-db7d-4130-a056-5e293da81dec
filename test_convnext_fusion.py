#!/usr/bin/env python3
"""
测试ConvNeXt特征融合的脚本
验证修改后的FFM模块是否能正确融合ConvNeXt特征
"""

import torch
import torch.nn as nn
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.FFM_change import FeatureFusionModule, CrossAttention, ConvNeXtFusionGate

def test_convnext_fusion_gate():
    """测试ConvNeXt融合门控模块"""
    print("=== 测试ConvNeXt融合门控模块 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 测试参数
    dim = 96
    num_heads = 8
    batch_size = 2
    H, W = 16, 16
    N = H * W
    
    # 创建测试数据
    original_feature = torch.randn(batch_size, N, dim).to(device)
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
    
    print(f"原始特征形状: {original_feature.shape}")
    print(f"ConvNeXt特征形状: {convnext_feature.shape}")
    
    try:
        # 测试ConvNeXt融合门控模块
        gate = ConvNeXtFusionGate(dim, num_heads).to(device)
        enhanced_feature = gate(original_feature, convnext_feature)
        
        print(f"增强后特征形状: {enhanced_feature.shape}")
        print("✓ ConvNeXt融合门控模块测试成功！")
        
        return True
    except Exception as e:
        print(f"✗ ConvNeXt融合门控模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cross_attention_with_convnext():
    """测试支持ConvNeXt特征的CrossAttention"""
    print("\n=== 测试支持ConvNeXt特征的CrossAttention ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    num_heads = 8
    batch_size = 2
    H, W = 16, 16
    N = H * W
    
    # 创建测试数据
    x1 = torch.randn(batch_size, N, dim).to(device)
    x2 = torch.randn(batch_size, N, dim).to(device)
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
    
    print(f"输入x1形状: {x1.shape}")
    print(f"输入x2形状: {x2.shape}")
    print(f"ConvNeXt特征形状: {convnext_feature.shape}")
    
    try:
        # 测试不使用ConvNeXt特征
        cross_attn = CrossAttention(dim, num_heads, enable_convnext_fusion=False).to(device)
        out1, out2 = cross_attn(x1, x2)
        print(f"不使用ConvNeXt - 输出1形状: {out1.shape}, 输出2形状: {out2.shape}")
        
        # 测试使用ConvNeXt特征
        cross_attn_with_convnext = CrossAttention(dim, num_heads, enable_convnext_fusion=True).to(device)
        out1_enhanced, out2_enhanced = cross_attn_with_convnext(x1, x2, convnext_feature)
        print(f"使用ConvNeXt - 输出1形状: {out1_enhanced.shape}, 输出2形状: {out2_enhanced.shape}")
        
        print("✓ CrossAttention with ConvNeXt测试成功！")
        return True
    except Exception as e:
        print(f"✗ CrossAttention with ConvNeXt测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_fusion_module():
    """测试完整的FeatureFusionModule"""
    print("\n=== 测试完整的FeatureFusionModule ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试参数
    dim = 96
    batch_size = 2
    H, W = 16, 16
    
    # 创建测试数据
    x1 = torch.randn(batch_size, dim, H, W).to(device)  # RGB特征
    x2 = torch.randn(batch_size, dim, H, W).to(device)  # Depth特征
    convnext_feature = torch.randn(batch_size, dim, H, W).to(device)  # ConvNeXt特征
    
    print(f"RGB特征形状: {x1.shape}")
    print(f"Depth特征形状: {x2.shape}")
    print(f"ConvNeXt特征形状: {convnext_feature.shape}")
    
    try:
        # 测试不使用ConvNeXt特征
        ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=False).to(device)
        out_without_convnext = ffm(x1, x2)
        print(f"不使用ConvNeXt - 输出形状: {out_without_convnext.shape}")
        
        # 测试使用ConvNeXt特征
        ffm_with_convnext = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(device)
        out_with_convnext = ffm_with_convnext(x1, x2, convnext_feature)
        print(f"使用ConvNeXt - 输出形状: {out_with_convnext.shape}")
        
        print("✓ FeatureFusionModule测试成功！")
        return True
    except Exception as e:
        print(f"✗ FeatureFusionModule测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dimension_compatibility():
    """测试不同维度的兼容性"""
    print("\n=== 测试不同维度的兼容性 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试不同的维度组合
    test_configs = [
        {"dim": 96, "H": 16, "W": 16},
        {"dim": 192, "H": 8, "W": 8},
        {"dim": 384, "H": 4, "W": 4},
        {"dim": 768, "H": 2, "W": 2},
    ]
    
    for config in test_configs:
        dim = config["dim"]
        H, W = config["H"], config["W"]
        batch_size = 2
        
        print(f"\n测试配置: dim={dim}, H={H}, W={W}")
        
        try:
            # 创建测试数据
            x1 = torch.randn(batch_size, dim, H, W).to(device)
            x2 = torch.randn(batch_size, dim, H, W).to(device)
            convnext_feature = torch.randn(batch_size, dim, H, W).to(device)
            
            # 测试FFM
            ffm = FeatureFusionModule(dim=dim, enable_convnext_fusion=True).to(device)
            output = ffm(x1, x2, convnext_feature)
            
            print(f"  输入形状: {x1.shape}")
            print(f"  输出形状: {output.shape}")
            print(f"  ✓ 配置 {config} 测试成功")
            
        except Exception as e:
            print(f"  ✗ 配置 {config} 测试失败: {e}")
            return False
    
    print("✓ 所有维度兼容性测试通过！")
    return True

def main():
    """主测试函数"""
    print("开始测试ConvNeXt特征融合功能...")
    
    all_tests_passed = True
    
    # 运行所有测试
    tests = [
        test_convnext_fusion_gate,
        test_cross_attention_with_convnext,
        test_feature_fusion_module,
        test_dimension_compatibility
    ]
    
    for test_func in tests:
        if not test_func():
            all_tests_passed = False
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("🎉 所有测试通过！ConvNeXt特征融合功能正常工作。")
    else:
        print("❌ 部分测试失败，请检查代码。")
    print("="*50)

if __name__ == "__main__":
    main()
